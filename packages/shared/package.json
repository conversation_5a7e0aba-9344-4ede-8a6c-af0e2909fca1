{"name": "@micro-core/shared", "version": "0.1.0", "description": "微前端框架共享基础设施包", "keywords": ["micro-frontend", "microfrontend", "shared", "utilities", "typescript"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared"}, "homepage": "https://github.com/echo008/micro-core/tree/main/packages/shared#readme", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md", "CHANGELOG.md"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.esm.js", "require": "./dist/utils.js"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.esm.js", "require": "./dist/types.js"}, "./constants": {"types": "./dist/constants.d.ts", "import": "./dist/constants.esm.js", "require": "./dist/constants.js"}, "./enums": {"types": "./dist/enums.d.ts", "import": "./dist/enums.esm.js", "require": "./dist/enums.js"}, "./errors": {"types": "./dist/errors.d.ts", "import": "./dist/errors.esm.js", "require": "./dist/errors.js"}, "./components": {"types": "./dist/components.d.ts", "import": "./dist/components.esm.js", "require": "./dist/components.js"}}, "scripts": {"build": "vite build", "build:watch": "vite build --watch", "dev": "vite build --watch", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "pnpm run clean && pnpm run build"}, "dependencies": {}, "devDependencies": {"@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.17.0", "rimraf": "^6.0.1", "typescript": "^5.7.2", "vite": "^7.0.6", "vite-plugin-dts": "^4.3.0", "vitest": "^3.2.4"}, "peerDependencies": {}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "sideEffects": false}
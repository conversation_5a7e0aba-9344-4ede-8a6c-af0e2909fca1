import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ErrorMonitor } from '../../src/errors/monitor'
import { MicroCoreError } from '../../src/errors/micro-core'
import { ErrorCodes, ErrorSeverity, ErrorCategory } from '../../src/constants/errors'

describe('ErrorMonitor', () => {
  let errorMonitor: ErrorMonitor
  let mockLogger: any
  let mockReporter: any

  beforeEach(() => {
    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn()
    }

    mockReporter = {
      report: vi.fn().mockResolvedValue(true)
    }

    errorMonitor = new ErrorMonitor({
      autoReport: false, // 禁用自动报告避免干扰
      reportThreshold: 5,
      reportInterval: 1000,
      maxHistory: 1000,
      maxErrors: 10000,
      enabled: true
    })

    // 清理之前的状态
    errorMonitor.clear()
  })

  describe('基础监控功能', () => {
    it('应该创建错误监控实例', () => {
      expect(errorMonitor).toBeInstanceOf(ErrorMonitor)
      expect(errorMonitor.isEnabled()).toBe(true)
    })

    it('应该记录错误', () => {
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      
      errorMonitor.recordError(error)
      
      const errors = errorMonitor.getErrors()
      expect(errors).toHaveLength(1)
      expect(errors[0]).toBe(error)
      expect(errors[0]?.timestamp).toBeTypeOf('number')
    })

    it('应该记录错误上下文', () => {
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED, {
        context: { userId: '123', sessionId: 'abc' }
      })
      
      errorMonitor.recordError(error)
      
      const errors = errorMonitor.getErrors()
      expect(errors[0]?.context).toEqual({ userId: '123', sessionId: 'abc' })
    })

    it('应该支持启用和禁用监控', () => {
      errorMonitor.disable()
      expect(errorMonitor.isEnabled()).toBe(false)
      
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)
      
      expect(errorMonitor.getErrors()).toHaveLength(0)
      
      errorMonitor.enable()
      expect(errorMonitor.isEnabled()).toBe(true)
    })
  })

  describe('错误统计', () => {
    it('应该统计错误数量', () => {
      const error1 = new MicroCoreError('错误1', ErrorCodes.APP_LOAD_FAILED)
      const error2 = new MicroCoreError('错误2', ErrorCodes.APP_MOUNT_FAILED)
      const error3 = new MicroCoreError('错误3', ErrorCodes.APP_LOAD_FAILED)
      
      errorMonitor.recordError(error1)
      errorMonitor.recordError(error2)
      errorMonitor.recordError(error3)
      
      const stats = errorMonitor.getStats()
      
      expect(stats.total).toBe(3)
      expect(stats.byCode[ErrorCodes.APP_LOAD_FAILED as string]).toBe(2)
      expect(stats.byCode[ErrorCodes.APP_MOUNT_FAILED as string]).toBe(1)
    })

    it('应该按类别统计错误', () => {
      const appError = new MicroCoreError('应用错误', ErrorCodes.APP_LOAD_FAILED)
      const sandboxError = new MicroCoreError('沙箱错误', ErrorCodes.SANDBOX_CREATE_FAILED)
      const pluginError = new MicroCoreError('插件错误', ErrorCodes.PLUGIN_LOAD_FAILED)
      
      errorMonitor.recordError(appError)
      errorMonitor.recordError(sandboxError)
      errorMonitor.recordError(pluginError)
      
      const stats = errorMonitor.getStats()
      
      expect(stats.byCategory[ErrorCategory.APPLICATION as keyof typeof stats.byCategory]).toBe(1)
      expect(stats.byCategory[ErrorCategory.SANDBOX as keyof typeof stats.byCategory]).toBe(1)
      expect(stats.byCategory[ErrorCategory.PLUGIN as keyof typeof stats.byCategory]).toBe(1)
    })

    it('应该按严重程度统计错误', () => {
      // 确保开始时状态是干净的
      errorMonitor.clear()

      // 验证初始状态
      const initialStats = errorMonitor.getStats()
      expect(initialStats.total).toBe(0)
      expect(initialStats.byLevel[ErrorSeverity.HIGH]).toBe(0)

      const criticalError = new MicroCoreError('严重错误', ErrorCodes.SYSTEM_CRASH)
      const highError = new MicroCoreError('高级错误', ErrorCodes.APP_LOAD_FAILED)
      const mediumError = new MicroCoreError('中级错误', ErrorCodes.CONFIG_MISSING)

      errorMonitor.recordError(criticalError)
      errorMonitor.recordError(highError)
      errorMonitor.recordError(mediumError)

      const stats = errorMonitor.getStats()

      expect(stats.byLevel[ErrorSeverity.CRITICAL as keyof typeof stats.byLevel]).toBe(1)
      expect(stats.byLevel[ErrorSeverity.HIGH as keyof typeof stats.byLevel]).toBe(1)
      expect(stats.byLevel[ErrorSeverity.MEDIUM as keyof typeof stats.byLevel]).toBe(1)
    })

    it('应该统计时间范围内的错误', () => {
      const now = Date.now()
      
      // 创建带有特定时间戳的错误
      const error1 = new MicroCoreError('错误1', ErrorCodes.APP_LOAD_FAILED, {
        timestamp: now - 2000
      })
      const error2 = new MicroCoreError('错误2', ErrorCodes.APP_LOAD_FAILED, {
        timestamp: now - 500
      })
      
      errorMonitor.recordError(error1)
      errorMonitor.recordError(error2)
      
      const recentErrors = errorMonitor.getErrors().filter(error => 
        error.timestamp >= now - 1000 && error.timestamp <= now
      )
      
      expect(recentErrors.length).toBe(1) // 只有error2在时间范围内
    })
  })

  describe('错误过滤', () => {
    it('应该支持按错误代码过滤', () => {
      const error1 = new MicroCoreError('错误1', ErrorCodes.APP_LOAD_FAILED)
      const error2 = new MicroCoreError('错误2', ErrorCodes.APP_MOUNT_FAILED)
      const error3 = new MicroCoreError('错误3', ErrorCodes.APP_LOAD_FAILED)
      
      errorMonitor.recordError(error1)
      errorMonitor.recordError(error2)
      errorMonitor.recordError(error3)
      
      const filtered = errorMonitor.getErrorsByCode(ErrorCodes.APP_LOAD_FAILED)
      
      expect(filtered).toHaveLength(2)
      expect(filtered[0]).toBe(error1)
      expect(filtered[1]).toBe(error3)
    })

    it('应该支持按类别过滤', () => {
      const appError = new MicroCoreError('应用错误', ErrorCodes.APP_LOAD_FAILED)
      const sandboxError = new MicroCoreError('沙箱错误', ErrorCodes.SANDBOX_CREATE_FAILED)
      
      errorMonitor.recordError(appError)
      errorMonitor.recordError(sandboxError)
      
      const filtered = errorMonitor.filterErrors(error => 
        error instanceof MicroCoreError && error.getCategory() === ErrorCategory.APPLICATION
      )
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0]).toBe(appError)
    })

    it('应该支持按严重程度过滤', () => {
      const criticalError = new MicroCoreError('严重错误', ErrorCodes.SYSTEM_CRASH)
      const errorError = new MicroCoreError('一般错误', ErrorCodes.APP_LOAD_FAILED)
      
      errorMonitor.recordError(criticalError)
      errorMonitor.recordError(errorError)
      
      const filtered = errorMonitor.getErrorsByLevel(ErrorSeverity.CRITICAL)
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0]).toBe(criticalError)
    })

    it('应该支持自定义过滤器', () => {
      const error1 = new MicroCoreError('重要错误', ErrorCodes.APP_LOAD_FAILED)
      const error2 = new MicroCoreError('普通错误', ErrorCodes.APP_LOAD_FAILED)
      
      errorMonitor.recordError(error1)
      errorMonitor.recordError(error2)
      
      const filtered = errorMonitor.filterErrors((error) => 
        error.message.includes('重要')
      )
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0]).toBe(error1)
    })
  })

  describe('错误报告', () => {
    it('应该自动报告错误', async () => {
      const consoleSpy = vi.spyOn(console, 'log')
      
      // 设置较低的报告阈值以触发自动报告
      errorMonitor.updateConfig({ reportThreshold: 1, autoReport: true })
      
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)
      
      // 等待自动报告
      await new Promise(resolve => setTimeout(resolve, 50))
      
      // 验证是否有控制台输出（可能是不同的格式）
      expect(consoleSpy).toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })

    it('应该批量报告错误', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      // 设置报告阈值为2并启用自动报告
      errorMonitor.updateConfig({ reportThreshold: 2, autoReport: true })

      const error1 = new MicroCoreError('错误1', ErrorCodes.APP_LOAD_FAILED)
      const error2 = new MicroCoreError('错误2', ErrorCodes.APP_LOAD_FAILED)

      errorMonitor.recordError(error1)
      errorMonitor.recordError(error2)

      // 等待批量报告
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
    })

    it('应该定时报告错误', async () => {
      vi.useFakeTimers()
      const consoleSpy = vi.spyOn(console, 'log')

      // 设置报告间隔为100ms并启用自动报告
      errorMonitor.updateConfig({ reportInterval: 100, autoReport: true })

      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)

      // 推进时间
      vi.advanceTimersByTime(100)
      await vi.runOnlyPendingTimersAsync()

      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
      vi.useRealTimers()
    })

    it('应该支持手动报告', async () => {
      const consoleSpy = vi.spyOn(console, 'log')
      errorMonitor.updateConfig({ autoReport: false })
      
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)
      
      await errorMonitor.report()
      
      expect(consoleSpy).toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })

    it('应该处理报告失败', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error')

      // 模拟报告失败
      const originalReport = errorMonitor.report
      errorMonitor.report = vi.fn().mockRejectedValue(new Error('报告失败'))

      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)

      // 手动调用报告方法来触发失败
      try {
        await errorMonitor.report()
      } catch (e) {
        // 预期的错误
      }

      await new Promise(resolve => setTimeout(resolve, 10))

      // 验证错误被记录
      expect(errorMonitor.report).toHaveBeenCalled()

      // 恢复原方法
      errorMonitor.report = originalReport
      consoleErrorSpy.mockRestore()
    })
  })

  describe('错误聚合', () => {
    it('应该聚合相同的错误', () => {
      const error1 = new MicroCoreError('相同错误', ErrorCodes.APP_LOAD_FAILED)
      const error2 = new MicroCoreError('相同错误', ErrorCodes.APP_LOAD_FAILED)
      const error3 = new MicroCoreError('不同错误', ErrorCodes.APP_LOAD_FAILED)
      
      errorMonitor.recordError(error1)
      errorMonitor.recordError(error2)
      errorMonitor.recordError(error3)
      
      const aggregated = errorMonitor.aggregateErrors()
      
      expect(aggregated.size).toBe(2)
      
      const sameErrorKey = `${ErrorCodes.APP_LOAD_FAILED}:相同错误`
      const sameErrorGroup = aggregated.get(sameErrorKey)
      
      expect(sameErrorGroup?.count).toBe(2)
      expect(sameErrorGroup?.errors).toHaveLength(2)
    })

    it('应该按时间窗口聚合错误', () => {
      const now = Date.now()
      
      // 创建带有特定时间戳的错误
      const error1 = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED, {
        timestamp: now
      })
      const error2 = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED, {
        timestamp: now + 5000 // 5秒后
      })
      
      errorMonitor.recordError(error1)
      errorMonitor.recordError(error2)
      
      const aggregated = errorMonitor.aggregateErrorsByTimeWindow(1000)
      
      expect(aggregated.size).toBe(2) // 超出时间窗口，应该分别聚合
    })
  })

  describe('错误趋势分析', () => {
    it('应该分析错误趋势', () => {
      const now = Date.now()
      const errors = [
        { time: now - 3600000, count: 2 }, // 1小时前
        { time: now - 1800000, count: 5 }, // 30分钟前
        { time: now - 900000, count: 8 },  // 15分钟前
        { time: now, count: 3 }            // 现在
      ]
      
      // 创建带有特定时间戳的错误
      errors.forEach(({ time, count }) => {
        for (let i = 0; i < count; i++) {
          const error = new MicroCoreError(`错误${i}`, ErrorCodes.APP_LOAD_FAILED, {
            timestamp: time
          })
          errorMonitor.recordError(error)
        }
      })
      
      const trend = errorMonitor.getErrorTrend(3600000) // 1小时内的趋势
      
      expect(trend).toHaveLength(10) // 分成10个时间段
      expect(trend[0]).toHaveProperty('timestamp')
      expect(trend[0]).toHaveProperty('count')
    })

    it('应该检测错误激增', () => {
      const now = Date.now()
      
      // 正常错误率
      for (let i = 0; i < 5; i++) {
        const error = new MicroCoreError(`正常错误${i}`, ErrorCodes.APP_LOAD_FAILED, {
          timestamp: now - 60000 + i * 1000
        })
        errorMonitor.recordError(error)
      }
      
      // 错误激增
      for (let i = 0; i < 20; i++) {
        const error = new MicroCoreError(`激增错误${i}`, ErrorCodes.APP_LOAD_FAILED, {
          timestamp: now - 10000 + i * 500
        })
        errorMonitor.recordError(error)
      }
      
      const spike = errorMonitor.detectErrorSpike(5, 300000)
      
      expect(spike).toBe(true) // 检测到错误激增
    })
  })

  describe('错误清理', () => {
    it('应该清理过期错误', () => {
      const now = Date.now()
      
      // 创建带有特定时间戳的错误
      const oldError = new MicroCoreError('旧错误', ErrorCodes.APP_LOAD_FAILED, {
        timestamp: now - 86400000 // 1天前
      })
      const newError = new MicroCoreError('新错误', ErrorCodes.APP_LOAD_FAILED, {
        timestamp: now
      })
      
      errorMonitor.recordError(oldError)
      errorMonitor.recordError(newError)
      
      expect(errorMonitor.getErrors()).toHaveLength(2)
      
      // 清理1小时前的错误
      const cleanedCount = errorMonitor.cleanupExpiredErrors(3600000)
      
      const remainingErrors = errorMonitor.getErrors()
      expect(remainingErrors).toHaveLength(1)
      expect(remainingErrors[0]).toBe(newError)
      expect(cleanedCount).toBe(1)
    })

    it('应该限制错误记录数量', () => {
      errorMonitor.updateConfig({ maxErrors: 5 })
      
      // 记录超过限制的错误
      for (let i = 0; i < 10; i++) {
        const error = new MicroCoreError(`错误${i}`, ErrorCodes.APP_LOAD_FAILED)
        errorMonitor.recordError(error)
      }
      
      const errors = errorMonitor.getErrors()
      expect(errors).toHaveLength(5)
      
      // 应该保留最新的错误
      expect(errors[errors.length - 1].message).toBe('错误9')
    })

    it('应该支持手动清空错误', () => {
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)
      
      expect(errorMonitor.getErrors()).toHaveLength(1)
      
      errorMonitor.clear()
      
      expect(errorMonitor.getErrors()).toHaveLength(0)
    })
  })

  describe('事件监听', () => {
    it('应该支持错误事件监听', () => {
      const listener = vi.fn()
      
      errorMonitor.on('error', listener)
      
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)
      
      expect(listener).toHaveBeenCalledWith(error)
    })

    it('应该支持错误激增事件', () => {
      const listener = vi.fn()
      
      errorMonitor.addListener(listener)
      
      // 触发错误激增
      for (let i = 0; i < 20; i++) {
        const error = new MicroCoreError(`错误${i}`, ErrorCodes.APP_LOAD_FAILED)
        errorMonitor.recordError(error)
      }
      
      expect(listener).toHaveBeenCalledTimes(20)
    })

    it('应该支持移除事件监听', () => {
      const listener = vi.fn()
      
      errorMonitor.on('error', listener)
      errorMonitor.off('error')
      
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)
      
      expect(listener).not.toHaveBeenCalled()
    })
  })

  describe('配置管理', () => {
    it('应该支持更新配置', () => {
      errorMonitor.updateConfig({
        maxErrors: 1000,
        reportThreshold: 10,
        autoReport: false
      })
      
      // 验证配置已更新（通过行为验证）
      expect(() => {
        for (let i = 0; i < 1001; i++) {
          const error = new MicroCoreError(`错误${i}`, ErrorCodes.APP_LOAD_FAILED)
          errorMonitor.recordError(error)
        }
      }).not.toThrow()
      
      const errors = errorMonitor.getErrors()
      expect(errors.length).toBeLessThanOrEqual(1000)
    })

    it('应该验证配置参数', () => {
      expect(() => {
        errorMonitor.updateConfig({ maxErrors: -1 })
      }).toThrow('maxErrors 必须大于 0')
      
      expect(() => {
        errorMonitor.updateConfig({ maxHistory: 0 })
      }).toThrow('maxHistory 必须大于 0')
      
      expect(() => {
        errorMonitor.updateConfig({ sampleRate: 1.5 })
      }).toThrow('sampleRate 必须在 0-1 之间')
    })
  })

  describe('边界情况', () => {
    it('应该处理空错误', () => {
      expect(() => {
        errorMonitor.recordError(null as any)
      }).not.toThrow()
      
      expect(errorMonitor.getErrors()).toHaveLength(0)
    })

    it('应该处理重复的错误实例', () => {
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      
      errorMonitor.recordError(error)
      errorMonitor.recordError(error) // 同一个实例
      
      expect(errorMonitor.getErrors()).toHaveLength(2)
    })

    it('应该处理大量错误', () => {
      const startTime = Date.now()
      
      // 记录大量错误
      for (let i = 0; i < 1000; i++) {
        const error = new MicroCoreError(`错误${i}`, ErrorCodes.APP_LOAD_FAILED)
        errorMonitor.recordError(error)
      }
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      // 性能测试：应该在合理时间内完成
      expect(duration).toBeLessThan(1000) // 1秒内
      expect(errorMonitor.getStats().total).toBe(1000)
    })

    it('应该处理无效的时间范围', () => {
      const error = new MicroCoreError('测试错误', ErrorCodes.APP_LOAD_FAILED)
      errorMonitor.recordError(error)
      
      // 无效的时间范围查询
      const recentErrors = errorMonitor.getErrors().filter(err => 
        err.timestamp >= Date.now() && err.timestamp <= Date.now() - 1000
      )
      
      expect(recentErrors.length).toBe(0)
    })
  })
})
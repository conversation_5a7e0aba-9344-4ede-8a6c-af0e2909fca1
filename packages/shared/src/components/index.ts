/**
 * @fileoverview 公共组件主入口
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// 加载组件
export * from './loading'
export { default as LoadingManager } from './loading'

// 错误边界组件
export * from './error-boundary'
export { default as ErrorBoundary } from './error-boundary'

// 开发工具组件
export * from './dev-tools'
// DevTools class exported as named export, not default

// 导入默认导出
import LoadingManagerClass from './loading'
import ErrorBoundaryClass from './error-boundary'
import { DevTools as DevToolsClass } from './dev-tools'

/**
 * 组件工具函数
 */
export class ComponentUtils {
  /**
   * 创建加载管理器
   */
  static createLoadingManager(config?: any) {
    return new LoadingManagerClass(config)
  }

  /**
   * 创建错误边界
   */
  static createErrorBoundary(config?: any) {
    return new ErrorBoundaryClass(config)
  }

  /**
   * 创建开发工具
   */
  static createDevTools(config?: any) {
    return new DevToolsClass(config)
  }
}

/**
 * 默认导出
 */
export default {
  LoadingManager: LoadingManagerClass,
  ErrorBoundary: ErrorBoundaryClass,
  DevTools: DevToolsClass,
  ComponentUtils
}
import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./test/setup.ts'],
    include: ['packages/**/__tests__/**/*.test.ts'],
    exclude: [
      'node_modules',
      'dist',
      'build',
      'lib',
      'es',
      'packages/examples'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/dist/**',
        '**/build/**',
        '**/lib/**',
        '**/es/**',
        'packages/examples/**'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    testTimeout: 10000,
    hookTimeout: 10000,
    teardownTimeout: 5000,
    isolate: true,
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        minThreads: 1,
        maxThreads: 4
      }
    },
    reporter: ['verbose']
  },
  resolve: {
    alias: {
      '@micro-core/core': resolve(__dirname, 'packages/core/src'),
      '@micro-core/shared': resolve(__dirname, 'packages/shared/src'),
      '@micro-core/router': resolve(__dirname, 'packages/router/src'),
      '@micro-core/store': resolve(__dirname, 'packages/store/src'),
      '@micro-core/communication': resolve(__dirname, 'packages/communication/src'),
      '@micro-core/dev-tools': resolve(__dirname, 'packages/dev-tools/src'),
      '@micro-core/cli': resolve(__dirname, 'packages/cli/src'),
      '@micro-core/plugins': resolve(__dirname, 'packages/plugins/src'),
      '@micro-core/auth': resolve(__dirname, 'packages/auth/src'),
      '@micro-core/adapters': resolve(__dirname, 'packages/adapters/src'),
      '@micro-core/builders': resolve(__dirname, 'packages/builders/src'),
      '@micro-core/compatibility': resolve(__dirname, 'packages/compatibility/src'),
      '@micro-core/resource': resolve(__dirname, 'packages/resource/src'),
      '@micro-core/sidecar': resolve(__dirname, 'packages/sidecar/src'),
      '@micro-core/tools': resolve(__dirname, 'packages/tools/src')
    }
  }
});